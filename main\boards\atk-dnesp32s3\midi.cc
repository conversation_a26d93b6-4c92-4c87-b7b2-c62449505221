#include "M5UnitSynth.h"
#include "audio_codec.h"
#include "board.h"
#include "esp_wifi.h"
#include "freertos/event_groups.h"
#include "iot/thing.h"
#include "mqtt_client.h"
#include <esp_log.h>
#include <iostream>
#include <vector>
#include <string>
#include <sstream>
#include <unordered_map>
#include <map>

#define TAG "Midi"

constexpr int MIDI_CHANNEL = 0;
constexpr int MAX_MIDI_CHANNELS = 16;

// 音乐合成参数配置
constexpr int DEFAULT_BPM = 120;           // 默认节拍速度（每分钟节拍数）
constexpr int WHOLE_NOTE_BEATS = 4;        // 全音符包含的节拍数
constexpr int DEFAULT_VELOCITY = 80;       // 默认力度
constexpr int MIN_NOTE_DURATION = 50;      // 最小音符时长（毫秒）
constexpr int MAX_NOTE_DURATION = 4000;    // 最大音符时长（毫秒）

std::string trim(const std::string &str)
{
    const auto first = str.find_first_not_of(" \t\r\n");
    if (first == std::string::npos)
        return "";
    const auto last = str.find_last_not_of(" \t\r\n");
    return str.substr(first, last - first + 1);
}

// 将节拍数转换为毫秒时长
int beatToMilliseconds(float beats, int bpm = DEFAULT_BPM) {
    // 计算公式：时长(ms) = (节拍数 / BPM) * 60 * 1000
    float duration = (beats / bpm) * 60.0f * 1000.0f;
    
    // 处理附点音符（负数表示附点）
    if (beats < 0) {
        float baseBeat = -beats;
        duration = (baseBeat * 1.5f / bpm) * 60.0f * 1000.0f;  // 附点音符 = 基础时长 * 1.5
    }
    
    // 限制在合理范围内
    return std::max(MIN_NOTE_DURATION, std::min(MAX_NOTE_DURATION, static_cast<int>(duration)));
}

std::vector<std::string> split(const std::string &s, char delimiter)
{
    std::vector<std::string> tokens;
    std::string token;
    std::stringstream tokenStream(s);
    while (std::getline(tokenStream, token, delimiter))
    {
        tokens.push_back(token);
    }
    return tokens;
}

// 解析和弦（支持同时播放多个音符）
std::vector<std::string> parseChord(const std::string& chordStr) {
    std::vector<std::string> notes;
    
    // 检查是否为和弦格式 [NOTE1+NOTE2+NOTE3]
    if (chordStr.front() == '[' && chordStr.back() == ']') {
        std::string content = chordStr.substr(1, chordStr.length() - 2);
        notes = split(content, '+');
    } else {
        notes.push_back(chordStr);
    }
    
    return notes;
}

// int noteNameToMidiNumber(const std::string &noteName)
// {
//     static const std::map<std::string, int> noteMap = {
//         {"NOTE_B0", 23}, {"NOTE_C1", 24}, {"NOTE_CS1", 25}, {"NOTE_D1", 26}, {"NOTE_DS1", 27}, {"NOTE_E1", 28}, {"NOTE_F1", 29}, {"NOTE_FS1", 30}, {"NOTE_G1", 31}, {"NOTE_GS1", 32}, {"NOTE_A1", 33}, {"NOTE_AS1", 34}, {"NOTE_B1", 35}, {"NOTE_C2", 36}, {"NOTE_CS2", 37}, {"NOTE_D2", 38}, {"NOTE_DS2", 39}, {"NOTE_E2", 40}, {"NOTE_F2", 41}, {"NOTE_FS2", 42}, {"NOTE_G2", 43}, {"NOTE_GS2", 44}, {"NOTE_A2", 45}, {"NOTE_AS2", 46}, {"NOTE_B2", 47}, {"NOTE_C3", 48}, {"NOTE_CS3", 49}, {"NOTE_D3", 50}, {"NOTE_DS3", 51}, {"NOTE_E3", 52}, {"NOTE_F3", 53}, {"NOTE_FS3", 54}, {"NOTE_G3", 55}, {"NOTE_GS3", 56}, {"NOTE_A3", 57}, {"NOTE_AS3", 58}, {"NOTE_B3", 59}, {"NOTE_C4", 60}, {"NOTE_CS4", 61}, {"NOTE_D4", 62}, {"NOTE_DS4", 63}, {"NOTE_E4", 64}, {"NOTE_F4", 65}, {"NOTE_FS4", 66}, {"NOTE_G4", 67}, {"NOTE_GS4", 68}, {"NOTE_A4", 69}, {"NOTE_AS4", 70}, {"NOTE_B4", 71}, {"NOTE_C5", 72}, {"NOTE_CS5", 73}, {"NOTE_D5", 74}, {"NOTE_DS5", 75}, {"NOTE_E5", 76}, {"NOTE_F5", 77}, {"NOTE_FS5", 78}, {"NOTE_G5", 79}, {"NOTE_GS5", 80}, {"NOTE_A5", 81}, {"NOTE_AS5", 82}, {"NOTE_B5", 83}, {"NOTE_C6", 84}, {"NOTE_CS6", 85}, {"NOTE_D6", 86}, {"NOTE_DS6", 87}, {"NOTE_E6", 88}, {"NOTE_F6", 89}, {"NOTE_FS6", 90}, {"NOTE_G6", 91}, {"NOTE_GS6", 92}, {"NOTE_A6", 93}, {"NOTE_AS6", 94}, {"NOTE_B6", 95}, {"NOTE_C7", 96}, {"NOTE_CS7", 97}, {"NOTE_D7", 98}, {"NOTE_DS7", 99}, {"NOTE_E7", 100}, {"NOTE_F7", 101}, {"NOTE_FS7", 102}, {"NOTE_G7", 103}, {"NOTE_GS7", 104}, {"NOTE_A7", 105}, {"NOTE_AS7", 106}, {"NOTE_B7", 107}, {"NOTE_C8", 108}, {"NOTE_CS8", 109}, {"NOTE_D8", 110}, {"NOTE_DS8", 111}};

//     const auto it = noteMap.find(noteName);
//     return (it != noteMap.end()) ? it->second : -1;
// }

int noteNameToMidiNumber(const std::string& noteName) {
    static const std::map<std::string, int> noteMap = {
        {"NOTE_B0", 23}, {"NOTE_C1", 24}, {"NOTE_CS1", 25}, {"NOTE_D1", 26}, {"NOTE_DS1", 27},
        {"NOTE_E1", 28}, {"NOTE_F1", 29}, {"NOTE_FS1", 30}, {"NOTE_G1", 31}, {"NOTE_GS1", 32},
        {"NOTE_A1", 33}, {"NOTE_AS1", 34}, {"NOTE_B1", 35}, {"NOTE_C2", 36}, {"NOTE_CS2", 37},
        {"NOTE_D2", 38}, {"NOTE_DS2", 39}, {"NOTE_E2", 40}, {"NOTE_F2", 41}, {"NOTE_FS2", 42},
        {"NOTE_G2", 43}, {"NOTE_GS2", 44}, {"NOTE_A2", 45}, {"NOTE_AS2", 46}, {"NOTE_B2", 47},
        {"NOTE_C3", 48}, {"NOTE_CS3", 49}, {"NOTE_D3", 50}, {"NOTE_DS3", 51}, {"NOTE_E3", 52},
        {"NOTE_F3", 53}, {"NOTE_FS3", 54}, {"NOTE_G3", 55}, {"NOTE_GS3", 56}, {"NOTE_A3", 57},
        {"NOTE_AS3", 58}, {"NOTE_B3", 59}, {"NOTE_C4", 60}, {"NOTE_CS4", 61}, {"NOTE_D4", 62},
        {"NOTE_DS4", 63}, {"NOTE_E4", 64}, {"NOTE_F4", 65}, {"NOTE_FS4", 66}, {"NOTE_G4", 67},
        {"NOTE_GS4", 68}, {"NOTE_A4", 69}, {"NOTE_AS4", 70}, {"NOTE_B4", 71}, {"NOTE_C5", 72},
        {"NOTE_CS5", 73}, {"NOTE_D5", 74}, {"NOTE_DS5", 75}, {"NOTE_E5", 76}, {"NOTE_F5", 77},
        {"NOTE_FS5", 78}, {"NOTE_G5", 79}, {"NOTE_GS5", 80}, {"NOTE_A5", 81}, {"NOTE_AS5", 82},
        {"NOTE_B5", 83}, {"NOTE_C6", 84}, {"NOTE_CS6", 85}, {"NOTE_D6", 86}, {"NOTE_DS6", 87},
        {"NOTE_E6", 88}, {"NOTE_F6", 89}, {"NOTE_FS6", 90}, {"NOTE_G6", 91}, {"NOTE_GS6", 92},
        {"NOTE_A6", 93}, {"NOTE_AS6", 94}, {"NOTE_B6", 95}, {"NOTE_C7", 96}, {"NOTE_CS7", 97},
        {"NOTE_D7", 98}, {"NOTE_DS7", 99}, {"NOTE_E7", 100}, {"NOTE_F7", 101}, {"NOTE_FS7", 102},
        {"NOTE_G7", 103}, {"NOTE_GS7", 104}, {"NOTE_A7", 105}, {"NOTE_AS7", 106}, {"NOTE_B7", 107},
        {"NOTE_C8", 108}, {"NOTE_CS8", 109}, {"NOTE_D8", 110}, {"NOTE_DS8", 111}
    };

    const auto it = noteMap.find(noteName);
    return (it != noteMap.end()) ? it->second : -1;
}




using InstrumentNotesMap = std::map<std::string, std::vector<std::string>>;

InstrumentNotesMap parseInstruments(const std::string &input)
{
    InstrumentNotesMap instruments;
    const auto lines = split(input, '\n');
    for (const auto &line : lines)
    {
        const auto colonPos = line.find(": ");
        if (colonPos == std::string::npos)
            continue;

        const auto instrument = trim(line.substr(0, colonPos));
        const auto notesStr = trim(line.substr(colonPos + 2));
        const auto notes = split(notesStr, ',');

        instruments[instrument] = notes;
    }
    return instruments;
}

const std::unordered_map<std::string, unit_synth_instrument_t>& getInstrumentMap() {
    static const std::unordered_map<std::string, unit_synth_instrument_t> instrumentMap = {
    // =============== 钢琴组 (0 - 7) ===============
    {"GrandPiano_1", GrandPiano_1},     // 大钢琴1
    {"BrightPiano_2", BrightPiano_2},   // 明亮钢琴2
    {"ElGrdPiano_3", ElGrdPiano_3},     // 电三角钢琴3
    {"HonkyTonkPiano", HonkyTonkPiano}, // 酒吧钢琴
    {"ElPiano1", ElPiano1},             // 电钢琴1 (FM)
    {"ElPiano2", ElPiano2},             // 电钢琴2 (FM)
    {"Harpsichord", Harpsichord},       // 大键琴
    {"Clavi", Clavi},                   // 击弦古钢琴

    // =============== 敲击乐器组 (8 - 15) ===============
    {"Celesta", Celesta},           // 钢片琴
    {"Glockenspiel", Glockenspiel}, // 钟琴
    {"MusicBox", MusicBox},         // 音乐盒
    {"Vibraphone", Vibraphone},     // 颤音琴
    {"Marimba", Marimba},           // 马林巴琴
    {"Xylophone", Xylophone},       // 木琴
    {"TubularBells", TubularBells}, // 管钟
    {"Santur", Santur},             // 桑图尔琴

    // =============== 风琴组 (16 - 23) ===============
    {"DrawbarOrgan", DrawbarOrgan},       // 拉杆风琴
    {"PercussiveOrgan", PercussiveOrgan}, // 打击式风琴
    {"RockOrgan", RockOrgan},             // 摇滚风琴
    {"ChurchOrgan", ChurchOrgan},         // 教堂风琴
    {"ReedOrgan", ReedOrgan},             // 簧风琴
    {"AccordionFrench", AccordionFrench}, // 法式手风琴
    {"Harmonica", Harmonica},             // 口琴
    {"TangoAccordion", TangoAccordion},   // 探戈手风琴

    // =============== 吉他组 (24 - 31) ===============
    {"AcGuitarNylon", AcGuitarNylon},       // 尼龙弦吉他
    {"AcGuitarSteel", AcGuitarSteel},       // 钢弦吉他
    {"AcGuitarJazz", AcGuitarJazz},         // 爵士吉他
    {"AcGuitarClean", AcGuitarClean},       // 清音吉他
    {"AcGuitarMuted", AcGuitarMuted},       // 闷音吉他
    {"OverdrivenGuitar", OverdrivenGuitar}, // 过载吉他
    {"DistortionGuitar", DistortionGuitar}, // 失真吉他
    {"GuitarHarmonics", GuitarHarmonics},   // 吉他泛音

    // =============== 贝司组 (32 - 39) ===============
    {"AcousticBass", AcousticBass}, // 原声贝司
    {"FingerBass", FingerBass},     // 指弹贝司
    {"PickedBass", PickedBass},     // 拨片贝司
    {"FretlessBass", FretlessBass}, // 无品贝司
    {"SlapBass1", SlapBass1},       // 击弦贝司1
    {"SlapBass2", SlapBass2},       // 击弦贝司2
    {"SynthBass1", SynthBass1},     // 合成贝司1
    {"SynthBass2", SynthBass2},     // 合成贝司2

    // =============== 弦乐器组 (40 - 55) ===============
    {"Violin", Violin},                     // 小提琴
    {"Viola", Viola},                       // 中提琴
    {"Cello", Cello},                       // 大提琴
    {"Contrabass", Contrabass},             // 低音提琴
    {"TremoloStrings", TremoloStrings},     // 颤音弦乐
    {"PizzicatoStrings", PizzicatoStrings}, // 拨弦弦乐
    {"OrchestralHarp", OrchestralHarp},     // 竖琴
    {"Timpani", Timpani},                   // 定音鼓
    {"StringEnsemble1", StringEnsemble1},   // 弦乐合奏1
    {"StringEnsemble2", StringEnsemble2},   // 弦乐合奏2
    {"SynthStrings1", SynthStrings1},       // 合成弦乐1
    {"SynthStrings2", SynthStrings2},       // 合成弦乐2
    {"ChoirAahs", ChoirAahs},               // 合唱“啊”声
    {"VoiceOohs", VoiceOohs},               // 人声“喔”声
    {"SynthVoice", SynthVoice},             // 合成人声
    {"OrchestraHit", OrchestraHit},         // 管弦乐撞击声

    // =============== 铜管乐器组 (56 - 63) ===============
    {"Trumpet", Trumpet},           // 小号
    {"Trombone", Trombone},         // 长号
    {"Tuba", Tuba},                 // 大号
    {"MutedTrumpet", MutedTrumpet}, // 弱音小号
    {"FrenchHorn", FrenchHorn},     // 法国号
    {"BrassSection", BrassSection}, // 铜管组
    {"SynthBrass1", SynthBrass1},   // 合成铜管1
    {"SynthBrass2", SynthBrass2},   // 合成铜管2

    // =============== 木管乐器组 (64 - 79) ===============
    {"SopranoSax", SopranoSax},   // 高音萨克斯风
    {"AltoSax", AltoSax},         // 次中音萨克斯风
    {"TenorSax", TenorSax},       // 中音萨克斯风
    {"BaritoneSax", BaritoneSax}, // 低音萨克斯风
    {"Oboe", Oboe},               // 双簧管
    {"EnglishHorn", EnglishHorn}, // 英国管
    {"Bassoon", Bassoon},         // 大管
    {"Clarinet", Clarinet},       // 单簧管
    {"Piccolo", Piccolo},         // 短笛
    {"Flute", Flute},             // 长笛
    {"Recorder", Recorder},       // 竖笛
    {"PanFlute", PanFlute},       // 排箫
    {"BlownBottle", BlownBottle}, // 吹瓶音效
    {"Shakuhachi", Shakuhachi},   // 尺八
    {"Whistle", Whistle},         // 口哨声
    {"Ocarina", Ocarina},         // 陶笛

    // =============== 合成主音组 (80 - 87) ===============
    {"Lead1Square", Lead1Square},     // 合成主音1（方波）
    {"Lead2Sawtooth", Lead2Sawtooth}, // 合成主音2（锯齿波）
    {"Lead3Calliope", Lead3Calliope}, // 合成主音3（汽笛风琴）
    {"Lead4Chiff", Lead4Chiff},       // 合成主音4（气声）
    {"Lead5Charang", Lead5Charang},   // 合成主音5（拨弦）
    {"Lead6Voice", Lead6Voice},       // 合成主音6（人声）
    {"Lead7Fifths", Lead7Fifths},     // 合成主音7（五度和声）
    {"Lead8BassLead", Lead8BassLead}, // 合成主音8（贝司主音）

    // =============== 合成垫音组 (88 - 95) ===============
    {"Pad1Fantasia", Pad1Fantasia},   // 合成垫音1（幻想）
    {"Pad2Warm", Pad2Warm},           // 合成垫音2（温暖）
    {"Pad3PolySynth", Pad3PolySynth}, // 合成垫音3（多音合成）
    {"Pad4Choir", Pad4Choir},         // 合成垫音4（合唱）
    {"Pad5Bowed", Pad5Bowed},         // 合成垫音5（拉弦）
    {"Pad6Metallic", Pad6Metallic},   // 合成垫音6（金属）
    {"Pad7Halo", Pad7Halo},           // 合成垫音7（光晕）
    {"Pad8Sweep", Pad8Sweep},         // 合成垫音8（扫频）

    // =============== 音效组 (96 - 103) ===============
    {"FX1Rain", FX1Rain},             // 音效1（雨声）
    {"FX2Soundtrack", FX2Soundtrack}, // 音效2（电影配乐）
    {"FX3Crystal", FX3Crystal},       // 音效3（水晶）
    {"FX4Atmosphere", FX4Atmosphere}, // 音效4（氛围）
    {"FX5Brightness", FX5Brightness}, // 音效5（明亮）
    {"FX6Goblins", FX6Goblins},       // 音效6（妖精）
    {"FX7Echoes", FX7Echoes},         // 音效7（回声）
    {"FX8SciFi", FX8SciFi},           // 音效8（科幻）

    // =============== 世界乐器组 (104 - 111) ===============
    {"Sitar", Sitar},       // 西塔琴
    {"Banjo", Banjo},       // 班卓琴
    {"Shamisen", Shamisen}, // 三味线
    {"Koto", Koto},         // 尺八
    {"Kalimba", Kalimba},   // 卡林巴琴
    {"BagPipe", BagPipe},   // 风笛
    {"Fiddle", Fiddle},     // 小提琴（民间风格）
    {"Shanai", Shanai},     // 唢呐

    // =============== 打击效果组 (112 - 119) ===============
    {"TinkleBell", TinkleBell},       // 铃铛声
    {"Agogo", Agogo},                 // 阿哥哥鼓
    {"SteelDrums", SteelDrums},       // 钢鼓
    {"Woodblock", Woodblock},         // 木鱼
    {"TaikoDrum", TaikoDrum},         // 太鼓
    {"MelodicTom", MelodicTom},       // 旋律通通鼓
    {"SynthDrum", SynthDrum},         // 合成鼓
    {"ReverseCymbal", ReverseCymbal}, // 反向镲

    // =============== 噪音类 (120 - 121) ===============
    {"GtFretNoise", GtFretNoise}, // 吉他品丝噪音
    {"BreathNoise", BreathNoise}, // 呼吸噪音

    // =============== 环境音效类 (122 - 127) ===============
    {"Seashore", Seashore},     // 海边音效
    {"BirdTweet", BirdTweet},   // 鸟鸣声
    {"TelephRing", TelephRing}, // 电话铃声
    {"Helicopter", Helicopter}, // 直升机音效
    {"Applause", Applause},     // 掌声
    {"Gunshot", Gunshot}        // 枪声
    };
    return instrumentMap;
}

const std::unordered_map<std::string, unit_synth_instrument_t>& getInstrumentMapChines() {
    static const std::unordered_map<std::string, unit_synth_instrument_t> instrumentMapChines = {
    // =============== 钢琴组 (0 - 7) ===============
    {"大钢琴1", GrandPiano_1},
    {"明亮钢琴2", BrightPiano_2},
    {"电三角钢琴3", ElGrdPiano_3},
    {"酒吧钢琴", HonkyTonkPiano},
    {"电钢琴1 (FM)", ElPiano1},
    {"电钢琴2 (FM)", ElPiano2},
    {"大键琴", Harpsichord},
    {"击弦古钢琴", Clavi},

    // =============== 敲击乐器组 (8 - 15) ===============
    {"钢片琴", Celesta},
    {"钟琴", Glockenspiel},
    {"音乐盒", MusicBox},
    {"颤音琴", Vibraphone},
    {"马林巴琴", Marimba},
    {"木琴", Xylophone},
    {"管钟", TubularBells},
    {"桑图尔琴", Santur},

    // =============== 风琴组 (16 - 23) ===============
    {"拉杆风琴", DrawbarOrgan},
    {"打击式风琴", PercussiveOrgan},
    {"摇滚风琴", RockOrgan},
    {"教堂风琴", ChurchOrgan},
    {"簧风琴", ReedOrgan},
    {"法式手风琴", AccordionFrench},
    {"口琴", Harmonica},
    {"探戈手风琴", TangoAccordion},

    // =============== 吉他组 (24 - 31) ===============
    {"尼龙弦吉他", AcGuitarNylon},
    {"钢弦吉他", AcGuitarSteel},
    {"爵士吉他", AcGuitarJazz},
    {"清音吉他", AcGuitarClean},
    {"闷音吉他", AcGuitarMuted},
    {"过载吉他", OverdrivenGuitar},
    {"失真吉他", DistortionGuitar},
    {"吉他泛音", GuitarHarmonics},

    // =============== 贝司组 (32 - 39) ===============
    {"原声贝司", AcousticBass},
    {"指弹贝司", FingerBass},
    {"拨片贝司", PickedBass},
    {"无品贝司", FretlessBass},
    {"击弦贝司1", SlapBass1},
    {"击弦贝司2", SlapBass2},
    {"合成贝司1", SynthBass1},
    {"合成贝司2", SynthBass2},

    // =============== 弦乐器组 (40 - 55) ===============
    {"小提琴", Violin},
    {"中提琴", Viola},
    {"大提琴", Cello},
    {"低音提琴", Contrabass},
    {"颤音弦乐", TremoloStrings},
    {"拨弦弦乐", PizzicatoStrings},
    {"竖琴", OrchestralHarp},
    {"定音鼓", Timpani},
    {"弦乐合奏1", StringEnsemble1},
    {"弦乐合奏2", StringEnsemble2},
    {"合成弦乐1", SynthStrings1},
    {"合成弦乐2", SynthStrings2},
    {"合唱“啊”声", ChoirAahs},
    {"人声“喔”声", VoiceOohs},
    {"合成人声", SynthVoice},
    {"管弦乐撞击声", OrchestraHit},

    // =============== 铜管乐器组 (56 - 63) ===============
    {"小号", Trumpet},
    {"长号", Trombone},
    {"大号", Tuba},
    {"弱音小号", MutedTrumpet},
    {"法国号", FrenchHorn},
    {"铜管组", BrassSection},
    {"合成铜管1", SynthBrass1},
    {"合成铜管2", SynthBrass2},

    // =============== 木管乐器组 (64 - 79) ===============
    {"高音萨克斯风", SopranoSax},
    {"次中音萨克斯风", AltoSax},
    {"中音萨克斯风", TenorSax},
    {"低音萨克斯风", BaritoneSax},
    {"双簧管", Oboe},
    {"英国管", EnglishHorn},
    {"大管", Bassoon},
    {"单簧管", Clarinet},
    {"短笛", Piccolo},
    {"长笛", Flute},
    {"竖笛", Recorder},
    {"排箫", PanFlute},
    {"吹瓶音效", BlownBottle},
    {"尺八", Shakuhachi},
    {"口哨声", Whistle},
    {"陶笛", Ocarina},

    // =============== 合成主音组 (80 - 87) ===============
    {"合成主音1(方波)", Lead1Square},
    {"合成主音2(锯齿波)", Lead2Sawtooth},
    {"合成主音3(汽笛风琴)", Lead3Calliope},
    {"合成主音4(气声)", Lead4Chiff},
    {"合成主音5(拨弦)", Lead5Charang},
    {"合成主音6(人声)", Lead6Voice},
    {"合成主音7(五度和声)", Lead7Fifths},
    {"合成主音8(贝司主音)", Lead8BassLead},

    // =============== 合成垫音组 (88 - 95) ===============
    {"合成垫音1(幻想)", Pad1Fantasia},
    {"合成垫音2(温暖)", Pad2Warm},
    {"合成垫音3(多音合成)", Pad3PolySynth},
    {"合成垫音4(合唱)", Pad4Choir},
    {"合成垫音5(拉弦)", Pad5Bowed},
    {"合成垫音6(金属)", Pad6Metallic},
    {"合成垫音7(光晕)", Pad7Halo},
    {"合成垫音8(扫频)", Pad8Sweep},

    // =============== 音效组 (96 - 103) ===============
    {"音效1(雨声)", FX1Rain},
    {"音效2(电影配乐)", FX2Soundtrack},
    {"音效3(水晶)", FX3Crystal},
    {"音效4(氛围)", FX4Atmosphere},
    {"音效5(明亮)", FX5Brightness},
    {"音效6(妖精)", FX6Goblins},
    {"音效7(回声)", FX7Echoes},
    {"音效8(科幻)", FX8SciFi},

    // =============== 世界乐器组 (104 - 111) ===============
    {"西塔琴", Sitar},
    {"班卓琴", Banjo},
    {"三味线", Shamisen},
    {"尺八", Koto},
    {"卡林巴琴", Kalimba},
    {"风笛", BagPipe},
    {"小提琴(民间风格)", Fiddle},
    {"唢呐", Shanai},

    // =============== 打击效果组 (112 - 119) ===============
    {"铃铛声", TinkleBell},
    {"阿哥哥鼓", Agogo},
    {"钢鼓", SteelDrums},
    {"木鱼", Woodblock},
    {"太鼓", TaikoDrum},
    {"旋律通通鼓", MelodicTom},
    {"合成鼓", SynthDrum},
    {"反向镲", ReverseCymbal},

    // =============== 噪音类 (120 - 121) ===============
    {"吉他品丝噪音", GtFretNoise},
    {"呼吸噪音", BreathNoise},

    // =============== 环境音效类 (122 - 127) ===============
    {"海边音效", Seashore},
    {"鸟鸣声", BirdTweet},
    {"电话铃声", TelephRing},
    {"直升机音效", Helicopter},
    {"掌声", Applause},
    {"枪声", Gunshot}
    };
    return instrumentMapChines;
}

    
// math the instructment_t with Englisht and Chinese
const std::unordered_map<std::string, unit_synth_instrument_t>& getInstrumentMapEC() {
    static const std::unordered_map<std::string, unit_synth_instrument_t> instrumentMapEC = {
    // =============== 钢琴组 (0 - 7) ===============
    {"GrandPiano_1", GrandPiano_1},
    {"大钢琴1", GrandPiano_1},
    {"BrightPiano_2", BrightPiano_2},
    {"明亮钢琴2", BrightPiano_2},
    {"ElGrdPiano_3", ElGrdPiano_3},
    {"电三角钢琴3", ElGrdPiano_3},
    {"HonkyTonkPiano", HonkyTonkPiano},
    {"酒吧钢琴", HonkyTonkPiano},
    {"ElPiano1", ElPiano1},
    {"电钢琴1 (FM)", ElPiano1},
    {"ElPiano2", ElPiano2},
    {"电钢琴2 (FM)", ElPiano2},
    {"Harpsichord", Harpsichord},
    {"大键琴", Harpsichord},
    {"Clavi", Clavi},
    {"击弦古钢琴", Clavi},

    // =============== 敲击乐器组 (8 - 15) ===============
    {"Celesta", Celesta},
    {"钢片琴", Celesta},
    {"Glockenspiel", Glockenspiel},
    {"钟琴", Glockenspiel},
    {"MusicBox", MusicBox},
    {"音乐盒", MusicBox},
    {"Vibraphone", Vibraphone},
    {"颤音琴", Vibraphone},
    {"Marimba", Marimba},
    {"马林巴琴", Marimba},
    {"Xylophone", Xylophone},
    {"木琴", Xylophone},
    {"TubularBells", TubularBells},
    {"管钟", TubularBells},
    {"Santur", Santur},
    {"桑图尔琴", Santur},

    // =============== 风琴组 (16 - 23) ===============
    {"DrawbarOrgan", DrawbarOrgan},
    {"拉杆风琴", DrawbarOrgan},
    {"PercussiveOrgan", PercussiveOrgan},
    {"打击式风琴", PercussiveOrgan},
    {"RockOrgan", RockOrgan},
    {"摇滚风琴", RockOrgan},
    {"ChurchOrgan", ChurchOrgan},
    {"教堂风琴", ChurchOrgan},
    {"ReedOrgan", ReedOrgan},
    {"簧风琴", ReedOrgan},
    {"AccordionFrench", AccordionFrench},
    {"法式手风琴", AccordionFrench},
    {"Harmonica", Harmonica},
    {"口琴", Harmonica},
    {"TangoAccordion", TangoAccordion},
    {"探戈手风琴", TangoAccordion},

    // =============== 吉他组 (24 - 31) ===============
    {"AcGuitarNylon", AcGuitarNylon},
    {"尼龙弦吉他", AcGuitarNylon},
    {"AcGuitarSteel", AcGuitarSteel},
    {"钢弦吉他", AcGuitarSteel},
    {"AcGuitarJazz", AcGuitarJazz},
    {"爵士吉他", AcGuitarJazz},
    {"AcGuitarClean", AcGuitarClean},
    {"清音吉他", AcGuitarClean},
    {"AcGuitarMuted", AcGuitarMuted},
    {"闷音吉他", AcGuitarMuted},
    {"OverdrivenGuitar", OverdrivenGuitar},
    {"过载吉他", OverdrivenGuitar},
    {"DistortionGuitar", DistortionGuitar},
    {"失真吉他", DistortionGuitar},
    {"GuitarHarmonics", GuitarHarmonics},
    {"吉他泛音", GuitarHarmonics},

    // =============== 贝司组 (32 - 39) ===============
    {"AcousticBass", AcousticBass},
    {"原声贝司", AcousticBass},
    {"FingerBass", FingerBass},
    {"指弹贝司", FingerBass},
    {"PickedBass", PickedBass},
    {"拨片贝司", PickedBass},
    {"FretlessBass", FretlessBass},
    {"无品贝司", FretlessBass},
    {"SlapBass1", SlapBass1},
    {"击弦贝司1", SlapBass1},
    {"SlapBass2", SlapBass2},
    {"击弦贝司2", SlapBass2},
    {"SynthBass1", SynthBass1},
    {"合成贝司1", SynthBass1},
    {"SynthBass2", SynthBass2},
    {"合成贝司2", SynthBass2},

    // =============== 弦乐器组 (40 - 55) ===============
    {"Violin", Violin},
    {"小提琴", Violin},
    {"Viola", Viola},
    {"中提琴", Viola},
    {"Cello", Cello},
    {"大提琴", Cello},
    {"Contrabass", Contrabass},
    {"低音提琴", Contrabass},
    {"TremoloStrings", TremoloStrings},
    {"颤音弦乐", TremoloStrings},
    {"PizzicatoStrings", PizzicatoStrings},
    {"拨弦弦乐", PizzicatoStrings},
    {"OrchestralHarp", OrchestralHarp},
    {"竖琴", OrchestralHarp},
    {"Timpani", Timpani},
    {"定音鼓", Timpani},
    {"StringEnsemble1", StringEnsemble1},
    {"弦乐合奏1", StringEnsemble1},
    {"StringEnsemble2", StringEnsemble2},
    {"弦乐合奏2", StringEnsemble2},
    {"SynthStrings1", SynthStrings1},
    {"合成弦乐1", SynthStrings1},
    {"SynthStrings2", SynthStrings2},
    {"合成弦乐2", SynthStrings2},
    {"ChoirAahs", ChoirAahs},
    {"合唱“啊”声", ChoirAahs},
    {"VoiceOohs", VoiceOohs},
    {"人声“喔”声", VoiceOohs},
    {"SynthVoice", SynthVoice},
    {"合成人声", SynthVoice},
    {"OrchestraHit", OrchestraHit},
    {"管弦乐撞击声", OrchestraHit},

    // =============== 铜管乐器组 (56 - 63) ===============
    {"Trumpet", Trumpet},
    {"小号", Trumpet},
    {"Trombone", Trombone},
    {"长号", Trombone},
    {"Tuba", Tuba},
    {"大号", Tuba},
    {"MutedTrumpet", MutedTrumpet},
    {"弱音小号", MutedTrumpet},
    {"FrenchHorn", FrenchHorn},
    {"法国号", FrenchHorn},
    {"BrassSection", BrassSection},
    {"铜管组", BrassSection},
    {"SynthBrass1", SynthBrass1},
    {"合成铜管1", SynthBrass1},
    {"SynthBrass2", SynthBrass2},
    {"合成铜管2", SynthBrass2},

    // =============== 木管乐器组 (64 - 79) ===============
    {"SopranoSax", SopranoSax},
    {"高音萨克斯风", SopranoSax},
    {"AltoSax", AltoSax},
    {"次中音萨克斯风", AltoSax},
    {"TenorSax", TenorSax},
    {"中音萨克斯风", TenorSax},
    {"BaritoneSax", BaritoneSax},
    {"低音萨克斯风", BaritoneSax},
    {"Oboe", Oboe},
    {"双簧管", Oboe},
    {"EnglishHorn", EnglishHorn},
    {"英国管", EnglishHorn},
    {"Bassoon", Bassoon},
    {"大管", Bassoon},
    {"Clarinet", Clarinet},
    {"单簧管", Clarinet},
    {"Piccolo", Piccolo},
    {"短笛", Piccolo},
    {"Flute", Flute},
    {"长笛", Flute},
    {"Recorder", Recorder},
    {"竖笛", Recorder},
    {"PanFlute", PanFlute},
    {"排箫", PanFlute},
    {"BlownBottle", BlownBottle},
    {"吹瓶音效", BlownBottle},
    {"Shakuhachi", Shakuhachi},
    {"尺八", Shakuhachi},
    {"Whistle", Whistle},
    {"口哨声", Whistle},
    {"Ocarina", Ocarina},
    {"陶笛", Ocarina},

    // =============== 合成主音组 (80 - 87) ===============
    {"Lead1Square", Lead1Square},
    {"合成主音1(方波)", Lead1Square},
    {"Lead2Sawtooth", Lead2Sawtooth},
    {"合成主音2(锯齿波)", Lead2Sawtooth},
    {"Lead3Calliope", Lead3Calliope},
    {"合成主音3(汽笛风琴)", Lead3Calliope},
    {"Lead4Chiff", Lead4Chiff},
    {"合成主音4(气声)", Lead4Chiff},
    {"Lead5Charang", Lead5Charang},
    {"合成主音5(拨弦)", Lead5Charang},
    {"Lead6Voice", Lead6Voice},
    {"合成主音6(人声)", Lead6Voice},
    {"Lead7Fifths", Lead7Fifths},
    {"合成主音7(五度和声)", Lead7Fifths},
    {"Lead8BassLead", Lead8BassLead},
    {"合成主音8(贝司主音)", Lead8BassLead},

    // =============== 合成垫音组 (88 - 95) ===============
    {"Pad1Fantasia", Pad1Fantasia},
    {"合成垫音1(幻想)", Pad1Fantasia},
    {"Pad2Warm", Pad2Warm},
    {"合成垫音2(温暖)", Pad2Warm},
    {"Pad3PolySynth", Pad3PolySynth},
    {"合成垫音3(多音合成)", Pad3PolySynth},
    {"Pad4Choir", Pad4Choir},
    {"合成垫音4(合唱)", Pad4Choir},
    {"Pad5Bowed", Pad5Bowed},
    {"合成垫音5(拉弦)", Pad5Bowed},
    {"Pad6Metallic", Pad6Metallic},
    {"合成垫音6(金属)", Pad6Metallic},
    {"Pad7Halo", Pad7Halo},
    {"合成垫音7(光晕)", Pad7Halo},
    {"Pad8Sweep", Pad8Sweep},
    {"合成垫音8(扫频)", Pad8Sweep},

    // =============== 音效组 (96 - 103) ===============
    {"FX1Rain", FX1Rain},
    {"音效1(雨声)", FX1Rain},
    {"FX2Soundtrack", FX2Soundtrack},
    {"音效2(电影配乐)", FX2Soundtrack},
    {"FX3Crystal", FX3Crystal},
    {"音效3(水晶)", FX3Crystal},
    {"FX4Atmosphere", FX4Atmosphere},
    {"音效4(氛围)", FX4Atmosphere},
    {"FX5Brightness", FX5Brightness},
    {"音效5(明亮)", FX5Brightness},
    {"FX6Goblins", FX6Goblins},
    {"音效6(妖精)", FX6Goblins},
    {"FX7Echoes", FX7Echoes},
    {"音效7(回声)", FX7Echoes},
    {"FX8SciFi", FX8SciFi},
    {"音效8(科幻)", FX8SciFi},

    // =============== 世界乐器组 (104 - 111) ===============
    {"Sitar", Sitar},
    {"西塔琴", Sitar},
    {"Banjo", Banjo},
    {"班卓琴", Banjo},
    {"Shamisen", Shamisen},
    {"三味线", Shamisen},
    {"Koto", Koto},
    {"尺八", Koto},
    {"Kalimba", Kalimba},
    {"卡林巴琴", Kalimba},
    {"BagPipe", BagPipe},
    {"风笛", BagPipe},
    {"Fiddle", Fiddle},
    {"小提琴(民间风格)", Fiddle},
    {"Shanai", Shanai},
    {"唢呐", Shanai},

    // =============== 打击效果组 (112 - 119) ===============
    {"TinkleBell", TinkleBell},
    {"铃铛声", TinkleBell},
    {"Agogo", Agogo},
    {"阿哥哥鼓", Agogo},
    {"SteelDrums", SteelDrums},
    {"钢鼓", SteelDrums},
    {"Woodblock", Woodblock},
    {"木鱼", Woodblock},
    {"TaikoDrum", TaikoDrum},
    {"太鼓", TaikoDrum},
    {"MelodicTom", MelodicTom},
    {"旋律通通鼓", MelodicTom},
    {"SynthDrum", SynthDrum},
    {"合成鼓", SynthDrum},
    {"ReverseCymbal", ReverseCymbal},
    {"反向镲", ReverseCymbal},

    // =============== 噪音类 (120 - 121) ===============
    {"GtFretNoise", GtFretNoise},
    {"吉他品丝噪音", GtFretNoise},
    {"BreathNoise", BreathNoise},
    {"呼吸噪音", BreathNoise},

    // =============== 环境音效类 (122 - 127) ===============
    {"Seashore", Seashore},
    {"海边音效", Seashore},
    {"BirdTweet", BirdTweet},
    {"鸟鸣声", BirdTweet},
    {"TelephRing", TelephRing},
    {"电话铃声", TelephRing},
    {"Helicopter", Helicopter},
    {"直升机音效", Helicopter},
    {"Applause", Applause},
    {"掌声", Applause},
    {"Gunshot", Gunshot},
    {"枪声", Gunshot}
    };
    return instrumentMapEC;
}

unit_synth_instrument_t getInstrumentFromString(const std::string &instrumentName)
{
    const auto& instrumentMap = getInstrumentMap();
    const auto it = instrumentMap.find(instrumentName);
    if (it != instrumentMap.end())
    {
        return it->second;
    }
    printf("Warning: Unknown instrument %s, using default GrandPiano_1\n", instrumentName.c_str());
    return GrandPiano_1;
}

std::unordered_map<std::string, int> instrumentChannelMap;
int nextChannel = 0;

int getMidiChannel(const std::string &instrument)
{
    const auto it = instrumentChannelMap.find(instrument);
    if (it != instrumentChannelMap.end())
    {
        return it->second;
    }

    const int assignedChannel = nextChannel % MAX_MIDI_CHANNELS;
    instrumentChannelMap[instrument] = assignedChannel;
    nextChannel++;
    return assignedChannel;
}

void playInstrumentTask(void *param)
{
    // 将传入的void指针转换为包含合成器实例的四元组指针
    // tuple结构：<合成器指针, 音符列表, MIDI通道号, BPM>
    auto *data = static_cast<std::tuple<M5UnitSynth *, std::vector<std::string>, int, int> *>(param);
    
    // 解包任务数据
    M5UnitSynth *synth = std::get<0>(*data);  // 获取合成器实例
    auto &notes = std::get<1>(*data);         // 获取当前乐器的音符序列引用
    const int midiChannel = std::get<2>(*data); // 获取分配的MIDI通道号
    const int bpm = std::get<3>(*data);       // 获取节拍速度

    // 遍历处理每个音符或和弦
    for (const auto &note : notes)
    {
        // 将音符字符串分割为名称-力度-节拍数三部分（例如"C4-80-1"或"[C4+E4+G4]-80-2"）
        const auto note_info = split(note, '-');
        if (note_info.size() < 2)  // 至少需要音符名和力度
            continue;

        // 解析音符要素
        const auto note_name = trim(note_info[0]);      // 音符名称或和弦
        const int velocity = (note_info.size() > 1) ? std::stoi(trim(note_info[1])) : DEFAULT_VELOCITY;
        const float beats = (note_info.size() > 2) ? std::stof(trim(note_info[2])) : 1.0f;  // 节拍数，支持小数和负数（附点）
        
        // 将节拍数转换为毫秒时长
        const int duration = beatToMilliseconds(beats, bpm);

        // 处理休止符（不发音）
        if (note_name == "REST") {
            vTaskDelay(pdMS_TO_TICKS(duration));
            continue;
        }

        // 解析和弦或单音符
        const auto chord_notes = parseChord(note_name);
        std::vector<int> active_notes;  // 记录激活的音符编号

        // 启动和弦中的所有音符
        for (const auto &single_note : chord_notes) {
            const auto clean_note = trim(single_note);
            const int note_num = noteNameToMidiNumber(clean_note);
            if (note_num != -1) {
                synth->setNoteOn(midiChannel, note_num, velocity);
                active_notes.push_back(note_num);
            }
        }

        // 维持音符时长
        vTaskDelay(pdMS_TO_TICKS(duration));

        // 关闭和弦中的所有音符
        for (const int note_num : active_notes) {
            synth->setNoteOff(midiChannel, note_num, velocity);
        }

        // 添加短暂间隔，避免音符粘连
        vTaskDelay(pdMS_TO_TICKS(10));
    }

    // 清理资源
    delete data;
    vTaskDelete(nullptr);
}

/*音符以及对应的时长。
解释了数字表示的音符时长含义，4 表示四分音符，8 表示八分音符，16 表示十六分音符，以此类推。
特别指出负数用于表示附点音符，例如 -4 表示附点四分音符，即一个四分音符加上一个八分音符。
*/


void playNotesMulti(M5UnitSynth &synth, const std::string &input, int bpm = DEFAULT_BPM) 
{
    // 解析输入的乐谱数据，支持BPM设置
    // 格式示例："BPM:140\nPiano: C4-80-1, E4-80-1; Violin: G5-90-0.5"
    std::string processedInput = input;
    int currentBpm = bpm;
    
    // 检查是否包含BPM设置
    size_t bpmPos = input.find("BPM:");
    if (bpmPos != std::string::npos) {
        size_t bpmEnd = input.find('\n', bpmPos);
        if (bpmEnd != std::string::npos) {
            std::string bpmStr = input.substr(bpmPos + 4, bpmEnd - bpmPos - 4);
            currentBpm = std::stoi(trim(bpmStr));
            processedInput = input.substr(bpmEnd + 1);  // 移除BPM行
        }
    }
    
    printf("Playing music at %d BPM\n", currentBpm);
    const auto instruments = parseInstruments(processedInput);

    // 遍历解析出的所有乐器及其音符序列
    for (const auto &[instrument, notes] : instruments) 
    {
        // 为当前乐器分配MIDI通道（自动轮询16个通道）
        const int channel = getMidiChannel(instrument);
        
        // 输出调试信息：乐器名称和分配的通道号
        printf("Playing %s on MIDI channel %d...\n", instrument.c_str(), channel);
        
        // 设置合成器的乐器音色
        synth.setInstrument(0, channel, getInstrumentFromString(instrument));
        
        // 创建任务数据包，包含：
        // 1. 合成器指针 2. 音符序列 3. MIDI通道号 4. BPM
        auto *data = new std::tuple<M5UnitSynth *, std::vector<std::string>, int, int>(
            &synth, notes, channel, currentBpm);
        
        // 创建FreeRTOS播放任务
        xTaskCreate(playInstrumentTask, instrument.c_str(), 4096, data, 1, nullptr);
    }
}

// 定义一个常量成员函数，用于根据通道号获取对应的乐器名称
const std::string getInstrumentByChannel(int channel) 
{
    // 遍历乐器通道映射表（instrumentChannelMap），该表存储了乐器名称和对应的通道号
    for (const auto &pair : instrumentChannelMap)
    {
        // 检查当前遍历到的通道号是否与传入的通道号相等
        if (pair.second == channel)
        {
            // 如果相等，返回当前乐器名称（pair.first）
            return pair.first;
        }
    }
    // 如果遍历完所有映射表都没有找到匹配的通道号，返回"Unknown"
    return "Unknown";
}
// [乐器1]: [音符1]-[力度1]-[时长1], [音符2]-[力度2]-[时长2], ...\r\n
// [乐器2]: [音符3]-[力度3]-[时长3], [音符4]-[力度4]-[时长4], ...\r\n
// 力度改统一设置。
namespace iot
{
    class Midi : public Thing
    {
    private:
        M5UnitSynth synth;

    public:
        Midi() : Thing("Midi", "音乐合成器")
        {
            synth.begin(UART_NUM_2, UNIT_SYNTH_BAUD, 18, 17);
            methods_.AddMethod("Play", "专业音乐合成器", ParameterList({Parameter("Notes", R"(🎼 **专业音乐作曲助手** 🎼

你是一位资深的音乐制作人和作曲家，精通各种音乐风格和编曲技巧。请根据用户需求创作专业级的音乐作品。

## 🎵 **核心创作理念**
- **旋律优先**：创作流畅、动听、有记忆点的主旋律
- **和声丰富**：运用和弦进行营造丰富的音乐层次
- **节奏多样**：巧妙运用不同节拍值创造节奏变化
- **音色搭配**：选择合适的乐器组合表达音乐情感
- **结构完整**：包含引子、主题、发展、高潮、结尾

## 🎹 **专业乐器配置**

**🎼 古典交响**：GrandPiano_1, Violin, Viola, Cello, Flute, Oboe, FrenchHorn, Timpani
**🎸 现代流行**：ElPiano1, AcGuitarSteel, FingerBass, SynthPad, SynthDrum, Lead1Square
**🎷 爵士蓝调**：ElPiano2, AltoSax, AcousticBass, Vibraphone, MutedTrumpet, SynthBrass1
**🎎 世界民族**：Koto, Shakuhachi, Sitar, BagPipe, Kalimba, TaikoDrum, Shamisen
**🎛️ 电子合成**：Lead2Sawtooth, SynthBass1, Pad3PolySynth, FX3Crystal, SynthDrum
**🎺 管弦乐队**：Trumpet, Trombone, Clarinet, StringEnsemble1, OrchestralHarp, TubularBells

## 🎼 **音域分配策略**
- **主旋律线**：NOTE_C4 ~ NOTE_C6（中高音区，最佳听感）
- **和声声部**：NOTE_G3 ~ NOTE_G5（中音区，和谐支撑）
- **低音基础**：NOTE_C2 ~ NOTE_C4（低音区，节奏基础）
- **装饰音效**：NOTE_C6 ~ NOTE_C7（高音区，点缀亮色）

## ⏱️ **节拍时值系统**（基于音乐理论）
- **全音符**：4.0（最长，用于长音和结尾）
- **二分音符**：2.0（中长音，抒情旋律）
- **四分音符**：1.0（标准节拍，主要旋律）
- **八分音符**：0.5（快速音符，活跃节奏）
- **十六分音符**：0.25（装饰音，技巧展示）
- **附点音符**：-1.0, -2.0（负数表示，增加1.5倍时长）
- **休止符**：REST-0-[节拍]（营造呼吸感和节奏感）

## 🎚️ **力度表现指南**
- **ff强奏**：100-127（高潮、强调）
- **f中强**：85-99（主旋律、重要声部）
- **mf中等**：70-84（一般旋律）
- **mp中弱**：55-69（伴奏、背景）
- **p轻柔**：40-54（细腻表达）
- **pp极弱**：20-39（神秘、远距离效果）

## 🎼 **和弦编写技巧**
使用方括号表示同时演奏的和弦：
- **大三和弦**：[NOTE_C4+NOTE_E4+NOTE_G4]-80-2.0
- **小三和弦**：[NOTE_A4+NOTE_C5+NOTE_E5]-75-2.0
- **七和弦**：[NOTE_C4+NOTE_E4+NOTE_G4+NOTE_B4]-85-4.0
- **挂留和弦**：[NOTE_C4+NOTE_F4+NOTE_G4]-70-1.5

## 📝 **标准输出格式**
```
BPM:120
[乐器名]: [音符/和弦]-[力度]-[节拍], [音符/和弦]-[力度]-[节拍], ...\r\n
[乐器名]: [音符/和弦]-[力度]-[节拍], [音符/和弦]-[力度]-[节拍], ...\r\n
```

## 💡 **专业创作示例**

**抒情钢琴曲**：
```
BPM:80
GrandPiano_1: NOTE_C4-75-2.0, NOTE_E4-80-1.0, NOTE_G4-85-1.5, [NOTE_C5+NOTE_E5]-90-4.0, REST-0-1.0\r\n
Violin: REST-0-2.0, NOTE_G5-70-0.5, NOTE_A5-75-0.5, NOTE_C6-80-2.0, NOTE_E6-85-2.0\r\n
```

**现代流行编曲**：
```
BPM:120
ElPiano1: [NOTE_C4+NOTE_E4+NOTE_G4]-80-1.0, [NOTE_F4+NOTE_A4+NOTE_C5]-80-1.0, [NOTE_G4+NOTE_B4+NOTE_D5]-85-2.0\r\n
FingerBass: NOTE_C2-90-1.0, NOTE_C2-70-0.5, NOTE_F2-90-1.0, NOTE_G2-90-1.5\r\n
SynthDrum: REST-0-0.25, NOTE_C2-100-0.25, REST-0-0.5, NOTE_C2-95-0.25\r\n
```

## 🎯 **创作要求**
1. **必须包含BPM设置**（60-180范围）
2. **至少使用2-4种乐器**创造层次
3. **运用和弦**丰富和声效果
4. **节拍变化**营造音乐起伏
5. **力度对比**表达音乐情感
6. **结构完整**有开始、发展、结尾

现在，请发挥你的专业音乐才华，创作出令人惊艳的音乐作品！)",
                                                                            kValueTypeString, true)}),
                               [this](const ParameterList &parameters)
                               {
                                   const std::string Notes = parameters["Notes"].string();
                                   printf("Notes: %s\n", Notes.c_str());
                                   playNotesMulti(synth, Notes);
                               });
        }
    };
} // namespace iot

DECLARE_THING(Midi);


// 
// 4 表示四分音符，8 表示八分音符，16 表示十六分音符，以此类推。 
//特别指出负数用于表示附点音符，例如 -4 表示附点四分音符，即一个四分音符加上一个八分音符。